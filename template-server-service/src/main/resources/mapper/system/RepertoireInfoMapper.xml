<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.RepertoireInfoMapper">

    <resultMap id="repertoireInfoMap" type="RepertoireInfoResponse">
        <id column="id" property="id"/>
        <result column="repertoire_id" property="repertoireId"/>
        <collection property="userAvatar" select="userAvatarQuery" column="repertoireId" ofType="String"  />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.common.core.domain.entity.RepertoireInfo">
        <id column="id" property="id"/>
        <result column="initiate_merchant_id" property="initiateMerchantId"/>
        <result column="release_merchant_id" property="releaseMerchantId"/>
        <result column="prov_id" property="provId"/>
        <result column="theater_id" property="theaterId"/>
        <result column="repertoire_id" property="repertoireId"/>
        <result column="temporary_flag" property="temporaryFlag"/>
        <result column="theater_pass" property="theaterPass"/>
        <result column="repertoire_pass" property="repertoirePass"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="audit" property="audit"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="userAvatarQuery" resultType="String">
        SELECT
            u.avatar
        FROM
            t_comment AS c
            LEFT JOIN t_user AS u ON u.id = c.user_id
        WHERE
            c.repertoire_id = #{repertoireId}
        LIMIT 5
    </select>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , initiate_merchant_id, release_merchant_id, prov_id, theater_id, repertoire_id, temporary_flag, theater_pass, repertoire_pass, sort, `status`, audit, create_by, create_time, update_by, update_time
    </sql>

    <select id="findRepertoireByTheaterId" resultMap="repertoireInfoMap">
        SELECT
            repertoire_id,
            repertoireName,
            cover_picture,
            introduction,
            like_count,
            dislike_count,
            fansFlag,
            comment_count,
            endTime,
            startTime,
            repertoireLabelName,
            FLOOR(((like_count / (like_count + dislike_count)) * 100)) AS like_ratio
        FROM
            (SELECT
                 ri.repertoire_id,
                 r.name AS repertoireName,
                 r.cover_picture,
                 r.introduction,
                 SUM(CASE WHEN k.type = 1 THEN 1 ELSE 0 END) AS like_count,
                 SUM(CASE WHEN k.type = 0 THEN 1 ELSE 0 END) AS dislike_count,
                 (SELECT COUNT(1) FROM t_user_treasure WHERE repertoire_id = ri.repertoire_id AND user_id = 1) AS fansFlag,
                 COUNT(DISTINCT c.user_id) AS comment_count,
                 MAX(rid.end_time) AS endTime,
                 MIN(rid.start_time) AS startTime,
                 GROUP_CONCAT(DISTINCT rl.name) AS repertoireLabelName
            FROM
                 t_repertoire_info AS ri
                 LEFT JOIN t_repertoire AS r ON r.id = ri.repertoire_id
                 LEFT JOIN t_kudos AS k ON k.repertoire_id = ri.repertoire_id
                 LEFT JOIN t_comment AS c ON c.repertoire_id = ri.repertoire_id
                 LEFT JOIN t_repertoire_info_detail AS rid ON rid.repertoire_info_id = ri.id
                 LEFT JOIN t_repertoire_label AS rl ON rl.repertoire_id = ri.repertoire_id
            WHERE
                ri.theater_id = #{theaterId}
                AND ri.audit = '2'
                AND ri.status = '1'
            GROUP BY
                ri.repertoire_id) AS subquery
            ORDER BY startTime ASC
    </select>
    <select id="findRepertoireTheaterInfo" resultType="repertoireInfoResponse">
        SELECT
            ri.id,
            ri.repertoire_id,
            ri.theater_id,
            t.`name` AS theaterName,
            r.`name` AS repertoireName,
            r.cover_picture AS repertoireCoverPicture,
            r.introduction AS repertoireIntroduction,
            ri.`status` AS repertoireInfoStatus,
            ri.audit AS repertoireInfoAudit,
            ri.temporary_flag AS repertoireInfoTemporaryFlag,
            ri.theater_pass AS repertoireInfoTheaterPass,
            ri.repertoire_pass AS repertoireInfoRepertoirePass,
            ri.sort AS repertoireInfoSort,
            ri.create_time AS repertoireInfoCreateTime,
            ri.update_time AS repertoireInfoUpdateTime
        FROM
            t_repertoire_info AS ri
            LEFT JOIN t_repertoire AS r ON r.id = ri.repertoire_id
            LEFT JOIN t_theater AS t ON t.id = ri.theater_id
        WHERE
            ri.repertoire_id = #{repertoireId}
            AND ri.`status` = '1'
            AND ri.audit = '2'
            AND ri.deleted = '1'
            AND r.`status` = '1'
            AND r.deleted = '1'
            AND t.`status` = '1'
            AND t.deleted = '1'
    </select>
</mapper>
