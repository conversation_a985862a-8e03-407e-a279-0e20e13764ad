<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.youying.system.mapper.EticketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.youying.system.domain.entity.Eticket">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="cover_image" property="coverImage"/>
        <result column="description" property="description"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, cover_image, description, create_time
    </sql>

    <!-- 分页查询电子票列表 -->
    <select id="selectEticketListByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_eticket
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') 
                OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据名称查询电子票列表 -->
    <select id="selectEticketList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_eticket
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询电子票 -->
    <select id="selectEticketById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_eticket
        WHERE id = #{id}
    </select>

    <!-- 插入电子票 -->
    <insert id="insertEticket" parameterType="com.youying.system.domain.entity.Eticket">
        INSERT INTO t_eticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="coverImage != null and coverImage != ''">cover_image,</if>
            <if test="description != null and description != ''">description,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="coverImage != null and coverImage != ''">#{coverImage},</if>
            <if test="description != null and description != ''">#{description},</if>
            NOW()
        </trim>
    </insert>

    <!-- 更新电子票 -->
    <update id="updateEticket" parameterType="com.youying.system.domain.entity.Eticket">
        UPDATE t_eticket
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="coverImage != null and coverImage != ''">cover_image = #{coverImage},</if>
            <if test="description != null and description != ''">description = #{description},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除电子票 -->
    <delete id="deleteEticketById">
        DELETE FROM t_eticket WHERE id = #{id}
    </delete>

    <!-- 批量删除电子票 -->
    <delete id="deleteEticketByIds">
        DELETE FROM t_eticket WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
