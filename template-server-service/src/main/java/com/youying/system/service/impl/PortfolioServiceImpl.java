package com.youying.system.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioResponse;
import com.youying.system.mapper.PortfolioMapper;
import com.youying.system.service.PortfolioService;
import com.youying.system.service.RepertoireInfoDetailService;

/**
 * <p>
 * 藏品组合表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Service
public class PortfolioServiceImpl extends ServiceImpl<PortfolioMapper, Portfolio> implements PortfolioService {
    @Autowired
    private RepertoireInfoDetailService repertoireInfoDetailService;

    /**
     * 查询藏品组合
     *
     * @param theaterId
     * @param repertoireId
     * @return
     */
    @Override
    public Portfolio findPortfolio(Long theaterId, Long repertoireId) {
        return getOne(new LambdaQueryWrapper<Portfolio>()
                .eq(Portfolio::getTheaterId, theaterId)
                .eq(Portfolio::getRepertoireId, repertoireId));
    }

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    @Override
    public PortfolioResponse findPortfolioByImageText(String imageText) {
        if (StringUtils.isEmpty(imageText)) {
            return null;
        }
        List<PortfolioResponse> portfolioList = baseMapper.findPortfolioList(null);

        for (PortfolioResponse portfolio : portfolioList) {
            String theaterShortName = portfolio.getTheaterShortName();
            String repertoireShortName = portfolio.getRepertoireShortName();

            if (containsAllKeywords(imageText, theaterShortName)
                    && containsAllKeywords(imageText, repertoireShortName)) {
                return portfolio;
            }
        }

        return null;
    }

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    @Override
    public PortfolioResponse findPortfolioByImageText(String imageText, List<Long> repertoireIdsList) {
        if (StringUtils.isEmpty(imageText)) {
            return null;
        }

        List<PortfolioResponse> portfolioList = baseMapper.findPortfolioList(repertoireIdsList);

        // 遍历组合商品，判断票面文字是否匹配
        for (PortfolioResponse portfolio : portfolioList) {
            String theaterShortName = portfolio.getTheaterShortName();
            String repertoireShortName = portfolio.getRepertoireShortName();

            if (containsAllKeywords(imageText, theaterShortName) && ifMatchKeyword(repertoireShortName, imageText)) {
                return portfolio;
            }
        }

        return null;
    }

    /**
     * 判断剧目短名称在票面文字中是否匹配，且匹配度多少
     * 
     * @param repertoireShortName
     * @param imageText
     * @return
     */
    private boolean ifMatchKeyword(String repertoireShortName, String imageText) {
        double matchDegree = com.youying.common.utils.StringUtils.matchDegree(repertoireShortName, imageText);
        if (matchDegree > 0.8) {
            return true;
        }
        return false;
    }

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    @Override
    public PortfolioResponse findPortfolioByImageText(List<Long> portfolioIdList, String imageText) {
        if (StringUtils.isEmpty(imageText)) {
            return null;
        }

        List<PortfolioResponse> portfolioList = baseMapper.findPortfolioList(null);

        for (PortfolioResponse portfolio : portfolioList) {
            String theaterShortName = portfolio.getTheaterShortName();
            String repertoireShortName = portfolio.getRepertoireShortName();

            if (!portfolioIdList.contains(portfolio.getId()) && containsAllKeywords(imageText, theaterShortName)
                    && containsAllKeywords(imageText, repertoireShortName)) {
                return portfolio;
            }
        }

        return null;
    }

    /**
     * 查询组合商品
     *
     * @return
     */
    @Override
    public List<PortfolioResponse> findPortfolioList() {
        return baseMapper.findPortfolioList(null);
    }

    private boolean containsAllKeywords(String text, String keywords) {
        if (StringUtils.isNotEmpty(keywords)) {
            String[] keywordList = keywords.split(",");
            for (String keyword : keywordList) {
                if (!text.toLowerCase().contains(keyword.replaceAll("\\s", "").toLowerCase())) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

}
