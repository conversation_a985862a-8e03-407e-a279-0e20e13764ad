package com.youying.system.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.RepertoireInfo;
import com.youying.system.domain.repertoireinfo.RepertoireInfoRequest;
import com.youying.system.domain.repertoireinfo.RepertoireInfoResponse;
import com.youying.system.mapper.RepertoireInfoMapper;
import com.youying.system.service.RepertoireInfoService;

/**
 * <p>
 * 剧目场次信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class RepertoireInfoServiceImpl extends ServiceImpl<RepertoireInfoMapper, RepertoireInfo>
        implements RepertoireInfoService {

    /**
     * 根据剧目查询关联剧目
     *
     * @param request
     * @return
     */
    @Override
    public List<RepertoireInfoResponse> findRepertoireByTheaterId(RepertoireInfoRequest request) {
        return baseMapper.findRepertoireByTheaterId(request);
    }

    /**
     * 查询剧目剧场信息
     *
     * @param repertoireId
     * @return
     */
    @Override
    public RepertoireInfoResponse findRepertoireTheaterInfo(Long repertoireId) {
        return baseMapper.findRepertoireTheaterInfo(repertoireId);
    }
}
