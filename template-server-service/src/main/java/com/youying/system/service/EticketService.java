package com.youying.system.service;

import java.util.List;

import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.entity.Eticket;

public interface EticketService {

    List<Eticket> listByPage(PageDomain pageDomain);

    List<Eticket> list(String name);

    Eticket detail(Integer id);

    int add(Eticket eticket);

    int update(Eticket eticket);

    int remove(Integer id);

    int removeBatch(List<Integer> ids);
}