package com.youying.system.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.page.PageDomain;
import com.youying.system.domain.entity.Eticket;
import com.youying.system.mapper.EticketMapper;
import com.youying.system.service.EticketService;

@Service
public class EticketServiceImpl extends ServiceImpl<EticketMapper, Eticket> implements EticketService {

    @Override
    public List<Eticket> listByPage(PageDomain pageDomain) {
        return baseMapper.selectEticketListByPage(pageDomain.getKeyword());
    }

    @Override
    public List<Eticket> list(String name) {
        return baseMapper.selectEticketList(name);
    }

    @Override
    public Eticket detail(Integer id) {
        return baseMapper.selectEticketById(id);
    }

    @Override
    public int add(Eticket eticket) {
        return baseMapper.insertEticket(eticket);
    }

    @Override
    public int update(Eticket eticket) {
        return baseMapper.updateEticket(eticket);
    }

    @Override
    public int remove(Integer id) {
        return baseMapper.deleteEticketById(id);
    }

    @Override
    public int removeBatch(List<Integer> ids) {
        return baseMapper.deleteEticketByIds(ids);
    }
}