package com.youying.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.youying.common.core.domain.entity.Portfolio;
import com.youying.system.domain.portfolio.PortfolioResponse;

import java.util.List;

/**
 * <p>
 * 藏品组合表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
public interface PortfolioService extends IService<Portfolio> {

    /**
     * 查询藏品组合
     *
     * @param theaterId
     * @param repertoireId
     * @return
     */
    Portfolio findPortfolio(Long theaterId, Long repertoireId);

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    PortfolioResponse findPortfolioByImageText(String imageText);

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @return
     */
    PortfolioResponse findPortfolioByImageText(String imageText, List<Long> repertoireIdsList);

    /**
     * 根据图片文字查询藏品信息
     *
     * @param imageText
     * @param portfolioIdList 不符合要求的组合id
     * @return
     */
    PortfolioResponse findPortfolioByImageText(List<Long> portfolioIdList, String imageText);

    /**
     * 查询组合商品
     *
     * @return
     */
    List<PortfolioResponse> findPortfolioList();
}
