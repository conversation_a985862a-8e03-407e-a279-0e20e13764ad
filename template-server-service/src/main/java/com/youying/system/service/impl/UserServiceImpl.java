package com.youying.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youying.common.core.domain.entity.User;
import com.youying.system.mapper.UserMapper;
import com.youying.system.service.UserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-28
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    /**
     * 根据手机查询用户信息
     *
     * @param phone
     * @return
     */
    @Override
    public User findUserByPhone(String phone) {
        return baseMapper.findUserByPhone(phone);
    }
}
