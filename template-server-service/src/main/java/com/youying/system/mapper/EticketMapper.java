package com.youying.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.youying.system.domain.entity.Eticket;

@Mapper
public interface EticketMapper extends BaseMapper<Eticket> {
    List<Eticket> selectEticketListByPage(@Param("keyword") String keyword);

    List<Eticket> selectEticketList(@Param("name") String name);

    Eticket selectEticketById(@Param("id") Integer id);

    int insertEticket(Eticket eticket);

    int updateEticket(Eticket eticket);

    int deleteEticketById(@Param("id") Integer id);

    int deleteEticketByIds(@Param("ids") List<Integer> ids);
}